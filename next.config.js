/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Enable ESLint during builds - all issues have been fixed
    ignoreDuringBuilds: false,
  },
  typescript: {
    // Enable TypeScript checking during builds
    ignoreBuildErrors: false,
  },
  // Optimize for production
  swcMinify: true,
  // Enable React strict mode
  reactStrictMode: true,
  // Production optimizations
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },
  // Image optimization for production
  images: {
    formats: ['image/webp', 'image/avif'],
  },
}

module.exports = nextConfig
